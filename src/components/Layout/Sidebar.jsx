import { useContext } from 'react';
import { KeyboardContext } from '../Keyboard/KeyboardContext';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { <PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'antd';
import { SwapOutlined, SettingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
const Sidebar = () => {
  const { data, setMenuItem, setCurrentLayer, setMaxSelection, setShowKeyboard, updateKeycap,
    setShowAdvancedKey, setShowLayerSwitch, setShowSelectButton, setCurrentSelectedKeycaps
  } = useContext(KeyboardContext);

  const { setAdvancedKey, addToQueue, setPerformance, firmwareVersion, newVersion } = useHandleDevice();
  const { t } = useTranslation();
  const items = [
    {
      key: 'keymap',
      icon: <svg width="24" height="24" viewBox="0 0 21 24" style={{marginRight: "20px"}} fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M18.2882 17.6457V2.68026C18.2882 2.42399 18.0785 2.21432 17.8223 2.21432H12.8716V0.210769C12.8716 0.0360412 12.6678 -0.0629713 12.5309 0.0447777L8.81503 2.98021C8.78989 2.99964 8.76953 3.02457 8.75552 3.05309C8.74152 3.08162 8.73423 3.11297 8.73423 3.14475C8.73423 3.17652 8.74152 3.20788 8.75552 3.2364C8.76953 3.26492 8.78989 3.28985 8.81503 3.30928L12.528 6.24471C12.6649 6.35246 12.8687 6.25636 12.8687 6.07872V4.07808H16.1886V17.6457C14.9014 18.0825 13.9753 19.3027 13.9753 20.7355C13.9753 22.5352 15.4372 23.9971 17.2369 23.9971C19.0366 23.9971 20.4985 22.5352 20.4985 20.7355C20.5014 19.3027 19.5754 18.0855 18.2882 17.6457ZM17.2398 22.1333C16.8741 22.1259 16.5258 21.9753 16.2697 21.714C16.0137 21.4526 15.8703 21.1014 15.8703 20.7355C15.8703 20.3696 16.0137 20.0184 16.2697 19.757C16.5258 19.4957 16.8741 19.3451 17.2398 19.3377C17.6056 19.3451 17.9539 19.4957 18.21 19.757C18.466 20.0184 18.6094 20.3696 18.6094 20.7355C18.6094 21.1014 18.466 21.4526 18.21 21.714C17.9539 21.9753 17.6056 22.1259 17.2398 22.1333ZM6.52318 3.26269C6.52318 1.46299 5.06129 0.00109564 3.26159 0.00109564C1.46189 0.00109564 0 1.46299 0 3.26269C0 4.69546 0.926059 5.91273 2.21322 6.35246V17.6486C0.926059 18.0855 0 19.3056 0 20.7384C0 22.5381 1.46189 24 3.26159 24C5.06129 24 6.52318 22.5381 6.52318 20.7384C6.52318 19.3056 5.59712 18.0884 4.30996 17.6486V6.35246C5.59712 5.91273 6.52318 4.69546 6.52318 3.26269ZM1.86377 3.26269C1.87123 2.8969 2.02178 2.54862 2.28311 2.29257C2.54444 2.03652 2.89573 1.89311 3.26159 1.89311C3.62745 1.89311 3.97874 2.03652 4.24007 2.29257C4.5014 2.54862 4.65195 2.8969 4.65942 3.26269C4.65195 3.62847 4.5014 3.97676 4.24007 4.23281C3.97874 4.48886 3.62745 4.63227 3.26159 4.63227C2.89573 4.63227 2.54444 4.48886 2.28311 4.23281C2.02178 3.97676 1.87123 3.62847 1.86377 3.26269ZM4.65942 20.7355C4.65195 21.1013 4.5014 21.4496 4.24007 21.7056C3.97874 21.9617 3.62745 22.1051 3.26159 22.1051C2.89573 22.1051 2.54444 21.9617 2.28311 21.7056C2.02178 21.4496 1.87123 21.1013 1.86377 20.7355C1.87123 20.3697 2.02178 20.0214 2.28311 19.7654C2.54444 19.5093 2.89573 19.3659 3.26159 19.3659C3.62745 19.3659 3.97874 19.5093 4.24007 19.7654C4.5014 20.0214 4.65195 20.3697 4.65942 20.7355Z" fill="white"/>
      </svg>
      ,
      label: t('sidebar.keymap'),
    },
    {
      key: 'light',
      icon: <svg width="26" height="26" viewBox="0 0 25 24" style={{marginRight: "18px"}} fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1049_10021)">
      <path d="M12.4996 0C11.8684 0 11.3486 0.512376 11.3486 1.15099V2.54703C11.3486 3.17822 11.861 3.69802 12.4996 3.69802C13.1382 3.69802 13.6506 3.18564 13.6506 2.54703V1.15099C13.6506 0.519802 13.1382 0 12.4996 0Z" fill="white"/>
      <path d="M12.4996 20.3096C11.8684 20.3096 11.3486 20.8219 11.3486 21.4606V22.8566C11.3486 23.4878 11.861 24.0076 12.4996 24.0076C13.1382 24.0076 13.6506 23.4952 13.6506 22.8566V21.4606C13.6506 20.8294 13.1382 20.3096 12.4996 20.3096Z" fill="white"/>
      <path d="M19.1907 6.45991C19.4951 6.45991 19.7847 6.3411 20.0001 6.12575L20.9803 5.13813C21.1956 4.92278 21.3144 4.63318 21.3144 4.32872C21.3144 4.01684 21.1956 3.72724 20.9803 3.51189C20.7649 3.29655 20.4753 3.17773 20.1709 3.17773C19.8664 3.17773 19.5768 3.29655 19.3615 3.51189L18.3813 4.49952C17.9357 4.94506 17.9357 5.67278 18.3813 6.12575C18.5966 6.3411 18.8862 6.45991 19.1907 6.45991Z" fill="white"/>
      <path d="M5.80931 17.54C5.50485 17.54 5.21525 17.6589 4.9999 17.8742L4.01971 18.8618C3.57416 19.3074 3.57416 20.0351 4.01971 20.4881C4.23505 20.7034 4.52466 20.8222 4.82911 20.8222C5.13357 20.8222 5.42317 20.7034 5.63852 20.4881L6.61872 19.5004C7.06426 19.0549 7.06426 18.3272 6.61872 17.8742C6.40337 17.6589 6.11377 17.54 5.80931 17.54Z" fill="white"/>
      <path d="M23.349 10.8486H21.953C21.3218 10.8486 20.802 11.361 20.802 11.9996C20.802 12.6382 21.3144 13.1506 21.953 13.1506H23.349C23.9802 13.1506 24.5 12.6382 24.5 11.9996C24.5 11.361 23.9876 10.8486 23.349 10.8486Z" fill="white"/>
      <path d="M4.19021 12.0006C4.19021 11.3694 3.67783 10.8496 3.03922 10.8496H1.64318C1.01199 10.8496 0.492188 11.362 0.492188 12.0006C0.492188 12.6392 1.00456 13.1516 1.64318 13.1516H3.03922C3.67041 13.1516 4.19021 12.6392 4.19021 12.0006Z" fill="white"/>
      <path d="M20.0001 17.8742C19.7847 17.6589 19.4951 17.54 19.1907 17.54C18.8862 17.54 18.5966 17.6589 18.3813 17.8742C17.9357 18.3197 17.9357 19.0475 18.3813 19.5004L19.3689 20.4881C19.5842 20.7034 19.8739 20.8222 20.1783 20.8222C20.4828 20.8222 20.7724 20.7034 20.9877 20.4881C21.4333 20.0425 21.4333 19.3148 20.9877 18.8618L20.0001 17.8742Z" fill="white"/>
      <path d="M5.63852 3.51189C5.42317 3.29655 5.13357 3.17773 4.82911 3.17773C4.52466 3.17773 4.23505 3.29655 4.01971 3.51189C3.80436 3.72724 3.68555 4.01684 3.68555 4.32872C3.68555 4.63318 3.80436 4.92278 4.01971 5.13813L5.00733 6.12575C5.22268 6.3411 5.51228 6.45991 5.81673 6.45991C6.12119 6.45991 6.41079 6.3411 6.62614 6.12575C7.07169 5.68021 7.07169 4.95249 6.62614 4.49952L5.63852 3.51189Z" fill="white"/>
      <path d="M12.5152 17.6584C9.39644 17.6584 6.86426 15.1262 6.86426 12.0074C6.86426 8.88862 9.39644 6.35645 12.5152 6.35645C15.6341 6.35645 18.1662 8.88862 18.1662 12.0074C18.1662 15.1262 15.6341 17.6584 12.5152 17.6584ZM12.5152 8.71041C10.6959 8.71041 9.21822 10.1881 9.21822 12C9.21822 13.8119 10.6959 15.2896 12.5152 15.2896C14.3346 15.2896 15.8049 13.8119 15.8049 12C15.8049 10.1881 14.3271 8.71041 12.5152 8.71041Z" fill="white"/>
      </g>
      <defs>
      <clipPath id="clip0_1049_10021">
      <rect width="24" height="24" fill="white" transform="translate(0.5)"/>
      </clipPath>
      </defs>
      </svg>
      ,
      label: t('sidebar.light'),
    },
    {
      key: 'performance',
      icon: <svg width="24" height="24" viewBox="0 0 24 24" style={{marginRight: "20px"}} fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M23.04 0H0.96C0.429 0 0 0.429 0 0.96V23.04C0 23.571 0.429 24 0.96 24H23.04C23.571 24 24 23.571 24 23.04V0.96C24 0.429 23.571 0 23.04 0ZM21.84 21.84H2.16V2.16H21.84V21.84ZM6.84 17.13V19.44C6.84 19.572 6.948 19.68 7.08 19.68H8.52C8.652 19.68 8.76 19.572 8.76 19.44V17.13C8.457 17.229 8.136 17.28 7.8 17.28C7.464 17.28 7.143 17.226 6.84 17.13ZM8.76 11.19V4.56C8.76 4.428 8.652 4.32 8.52 4.32H7.08C6.948 4.32 6.84 4.428 6.84 4.56V11.19C7.143 11.091 7.464 11.04 7.8 11.04C8.136 11.04 8.457 11.094 8.76 11.19ZM6.84 17.13C7.143 17.229 7.464 17.28 7.8 17.28C8.136 17.28 8.457 17.226 8.76 17.13C10.014 16.725 10.92 15.549 10.92 14.16C10.92 12.771 10.014 11.595 8.76 11.19C8.457 11.091 8.136 11.04 7.8 11.04C7.464 11.04 7.143 11.094 6.84 11.19C5.586 11.595 4.68 12.771 4.68 14.16C4.68 15.549 5.586 16.725 6.84 17.13ZM6.843 13.659C6.852 13.641 6.864 13.623 6.873 13.605V13.602L6.909 13.548C6.912 13.542 6.915 13.539 6.918 13.533C6.927 13.518 6.939 13.506 6.948 13.491C6.951 13.488 6.954 13.482 6.957 13.479C6.972 13.461 6.984 13.446 6.999 13.431L7.008 13.422L7.044 13.386L7.056 13.374C7.071 13.359 7.086 13.347 7.104 13.332C7.122 13.317 7.137 13.305 7.155 13.293C7.161 13.29 7.164 13.287 7.17 13.284C7.185 13.275 7.197 13.263 7.212 13.254C7.215 13.251 7.221 13.248 7.224 13.245C7.242 13.233 7.26 13.224 7.281 13.212C7.284 13.209 7.29 13.209 7.293 13.206C7.308 13.197 7.323 13.191 7.341 13.182L7.359 13.173C7.38 13.164 7.398 13.155 7.419 13.149C7.44 13.14 7.461 13.134 7.482 13.128C7.488 13.125 7.494 13.125 7.5 13.122C7.518 13.116 7.533 13.113 7.551 13.11C7.557 13.11 7.56 13.107 7.566 13.107C7.587 13.101 7.611 13.098 7.632 13.095C7.638 13.095 7.641 13.095 7.647 13.092C7.665 13.089 7.683 13.089 7.701 13.086H7.719C7.743 13.086 7.764 13.083 7.788 13.083C7.812 13.083 7.833 13.083 7.857 13.086H7.875C7.893 13.086 7.911 13.089 7.929 13.092C7.935 13.092 7.938 13.092 7.944 13.095C7.965 13.098 7.989 13.101 8.01 13.107C8.016 13.107 8.019 13.11 8.025 13.11C8.043 13.113 8.061 13.119 8.076 13.122C8.082 13.125 8.088 13.125 8.094 13.128C8.115 13.134 8.136 13.14 8.157 13.149C8.178 13.155 8.196 13.164 8.217 13.173L8.235 13.182C8.25 13.188 8.268 13.197 8.283 13.206C8.286 13.209 8.292 13.209 8.295 13.212C8.313 13.221 8.334 13.233 8.352 13.245C8.355 13.248 8.361 13.251 8.364 13.254C8.379 13.263 8.394 13.272 8.406 13.284C8.412 13.287 8.415 13.29 8.421 13.293C8.439 13.305 8.457 13.32 8.472 13.332C8.487 13.344 8.505 13.359 8.52 13.374L8.532 13.386L8.568 13.422L8.577 13.431C8.592 13.446 8.607 13.464 8.619 13.479C8.622 13.482 8.625 13.488 8.628 13.491C8.64 13.503 8.649 13.518 8.658 13.533C8.661 13.539 8.664 13.542 8.667 13.548L8.703 13.602C8.703 13.602 8.703 13.605 8.706 13.605C8.80603 13.7729 8.85889 13.9646 8.859 14.16C8.859 14.34 8.814 14.511 8.736 14.661C8.727 14.679 8.715 14.697 8.706 14.715C8.706 14.715 8.706 14.718 8.703 14.718L8.667 14.772C8.664 14.778 8.661 14.781 8.658 14.787C8.649 14.802 8.637 14.814 8.628 14.829C8.625 14.832 8.622 14.838 8.619 14.841C8.604 14.859 8.592 14.874 8.577 14.889L8.568 14.898L8.532 14.934L8.52 14.946C8.505 14.961 8.49 14.973 8.472 14.988C8.454 15.003 8.439 15.015 8.421 15.027C8.415 15.03 8.412 15.033 8.406 15.036C8.391 15.045 8.379 15.057 8.364 15.066C8.361 15.069 8.355 15.072 8.352 15.075C8.334 15.087 8.316 15.096 8.295 15.108C8.292 15.111 8.286 15.111 8.283 15.114C8.268 15.123 8.253 15.129 8.235 15.138L8.217 15.147C8.196 15.156 8.178 15.165 8.157 15.171C8.136 15.18 8.115 15.186 8.094 15.192C8.088 15.195 8.082 15.195 8.076 15.198C8.058 15.204 8.043 15.207 8.025 15.21C8.019 15.21 8.016 15.213 8.01 15.213C7.989 15.219 7.965 15.222 7.944 15.225C7.938 15.225 7.935 15.225 7.929 15.228C7.911 15.231 7.893 15.231 7.875 15.234H7.857C7.833 15.234 7.812 15.237 7.788 15.237C7.764 15.237 7.743 15.237 7.719 15.234H7.701C7.683 15.234 7.665 15.231 7.647 15.228C7.641 15.228 7.638 15.228 7.632 15.225C7.611 15.222 7.587 15.219 7.566 15.213C7.56 15.213 7.557 15.21 7.551 15.21C7.533 15.207 7.515 15.201 7.5 15.198C7.494 15.195 7.488 15.195 7.482 15.192C7.461 15.186 7.44 15.18 7.419 15.171C7.398 15.165 7.38 15.156 7.359 15.147L7.341 15.138C7.326 15.132 7.308 15.123 7.293 15.114C7.29 15.111 7.284 15.111 7.281 15.108C7.263 15.099 7.242 15.087 7.224 15.075C7.221 15.072 7.215 15.069 7.212 15.066C7.197 15.057 7.182 15.048 7.17 15.036C7.164 15.033 7.161 15.03 7.155 15.027C7.137 15.015 7.119 15 7.104 14.988C7.089 14.976 7.071 14.961 7.056 14.946L7.044 14.934L7.008 14.898L6.999 14.889C6.984 14.874 6.969 14.856 6.957 14.841C6.954 14.838 6.951 14.832 6.948 14.829C6.936 14.817 6.927 14.802 6.918 14.787C6.915 14.781 6.912 14.778 6.909 14.772L6.873 14.718V14.715C6.861 14.697 6.852 14.679 6.843 14.661C6.765 14.511 6.72 14.34 6.72 14.16C6.72 13.98 6.765 13.809 6.843 13.659ZM15.24 12.81V19.44C15.24 19.572 15.348 19.68 15.48 19.68H16.92C17.052 19.68 17.16 19.572 17.16 19.44V12.81C16.857 12.909 16.536 12.96 16.2 12.96C15.864 12.96 15.543 12.906 15.24 12.81ZM17.16 6.87V4.56C17.16 4.428 17.052 4.32 16.92 4.32H15.48C15.348 4.32 15.24 4.428 15.24 4.56V6.87C15.543 6.771 15.864 6.72 16.2 6.72C16.536 6.72 16.857 6.774 17.16 6.87ZM15.24 12.81C15.543 12.909 15.864 12.96 16.2 12.96C16.536 12.96 16.857 12.906 17.16 12.81C18.414 12.405 19.32 11.229 19.32 9.84C19.32 8.451 18.414 7.275 17.16 6.87C16.857 6.771 16.536 6.72 16.2 6.72C15.864 6.72 15.543 6.774 15.24 6.87C13.986 7.275 13.08 8.451 13.08 9.84C13.08 11.229 13.986 12.405 15.24 12.81ZM15.243 9.339C15.252 9.321 15.264 9.303 15.273 9.285V9.282L15.309 9.228C15.312 9.222 15.315 9.219 15.318 9.213C15.327 9.198 15.339 9.186 15.348 9.171C15.351 9.168 15.354 9.162 15.357 9.159C15.372 9.141 15.384 9.126 15.399 9.111L15.408 9.102L15.444 9.066L15.456 9.054C15.471 9.039 15.486 9.027 15.504 9.012C15.522 8.997 15.537 8.985 15.555 8.973C15.561 8.97 15.564 8.967 15.57 8.964C15.585 8.955 15.597 8.943 15.612 8.934C15.615 8.931 15.621 8.928 15.624 8.925C15.642 8.913 15.66 8.904 15.681 8.892C15.684 8.889 15.69 8.889 15.693 8.886C15.708 8.877 15.723 8.871 15.741 8.862L15.759 8.853C15.78 8.844 15.798 8.835 15.819 8.829C15.84 8.82 15.861 8.814 15.882 8.808C15.888 8.805 15.894 8.805 15.9 8.802C15.918 8.796 15.933 8.793 15.951 8.79C15.957 8.79 15.96 8.787 15.966 8.787C15.987 8.781 16.011 8.778 16.032 8.775C16.038 8.775 16.041 8.775 16.047 8.772C16.065 8.769 16.083 8.769 16.101 8.766H16.119C16.143 8.766 16.164 8.763 16.188 8.763C16.212 8.763 16.233 8.763 16.257 8.766H16.275C16.293 8.766 16.311 8.769 16.329 8.772C16.335 8.772 16.338 8.772 16.344 8.775C16.365 8.778 16.389 8.781 16.41 8.787C16.416 8.787 16.419 8.79 16.425 8.79C16.443 8.793 16.461 8.799 16.476 8.802C16.482 8.805 16.488 8.805 16.494 8.808C16.515 8.814 16.536 8.82 16.557 8.829C16.578 8.835 16.596 8.844 16.617 8.853L16.635 8.862C16.65 8.868 16.668 8.877 16.683 8.886C16.686 8.889 16.692 8.889 16.695 8.892C16.713 8.901 16.734 8.913 16.752 8.925C16.755 8.928 16.761 8.931 16.764 8.934C16.779 8.943 16.794 8.952 16.806 8.964C16.812 8.967 16.815 8.97 16.821 8.973C16.839 8.985 16.857 9 16.872 9.012C16.887 9.024 16.905 9.039 16.92 9.054L16.932 9.066L16.968 9.102L16.977 9.111C16.992 9.126 17.007 9.144 17.019 9.159C17.022 9.162 17.025 9.168 17.028 9.171C17.04 9.183 17.049 9.198 17.058 9.213C17.061 9.219 17.064 9.222 17.067 9.228L17.103 9.282V9.285C17.203 9.45286 17.2559 9.6446 17.256 9.84C17.256 10.02 17.211 10.191 17.133 10.341C17.124 10.359 17.112 10.377 17.103 10.395V10.398L17.067 10.452C17.064 10.458 17.061 10.461 17.058 10.467C17.049 10.482 17.037 10.494 17.028 10.509C17.025 10.512 17.022 10.518 17.019 10.521C17.004 10.539 16.992 10.554 16.977 10.569L16.968 10.578L16.932 10.614L16.92 10.626C16.905 10.641 16.89 10.653 16.872 10.668C16.854 10.683 16.839 10.695 16.821 10.707C16.815 10.71 16.812 10.713 16.806 10.716C16.791 10.725 16.779 10.737 16.764 10.746C16.761 10.749 16.755 10.752 16.752 10.755C16.734 10.767 16.716 10.776 16.695 10.788C16.692 10.791 16.686 10.791 16.683 10.794C16.668 10.803 16.653 10.809 16.635 10.818L16.617 10.827C16.596 10.836 16.578 10.845 16.557 10.851C16.536 10.86 16.515 10.866 16.494 10.872C16.488 10.875 16.482 10.875 16.476 10.878C16.458 10.884 16.443 10.887 16.425 10.89C16.419 10.89 16.416 10.893 16.41 10.893C16.389 10.899 16.365 10.902 16.344 10.905C16.338 10.905 16.335 10.905 16.329 10.908C16.311 10.911 16.293 10.911 16.275 10.914H16.257C16.233 10.914 16.212 10.917 16.188 10.917C16.164 10.917 16.143 10.917 16.119 10.914H16.101C16.083 10.914 16.065 10.911 16.047 10.908C16.041 10.908 16.038 10.908 16.032 10.905C16.011 10.902 15.987 10.899 15.966 10.893C15.96 10.893 15.957 10.89 15.951 10.89C15.933 10.887 15.915 10.881 15.9 10.878C15.894 10.875 15.888 10.875 15.882 10.872C15.861 10.866 15.84 10.86 15.819 10.851C15.798 10.845 15.78 10.836 15.759 10.827L15.741 10.818C15.726 10.812 15.708 10.803 15.693 10.794C15.69 10.791 15.684 10.791 15.681 10.788C15.663 10.779 15.642 10.767 15.624 10.755C15.621 10.752 15.615 10.749 15.612 10.746C15.597 10.737 15.582 10.728 15.57 10.716C15.564 10.713 15.561 10.71 15.555 10.707C15.537 10.695 15.519 10.68 15.504 10.668C15.489 10.656 15.471 10.641 15.456 10.626L15.444 10.614L15.408 10.578L15.399 10.569C15.384 10.554 15.369 10.536 15.357 10.521C15.354 10.518 15.351 10.512 15.348 10.509C15.336 10.497 15.327 10.482 15.318 10.467C15.315 10.461 15.312 10.458 15.309 10.452L15.273 10.398V10.395C15.261 10.377 15.252 10.359 15.243 10.341C15.165 10.191 15.12 10.02 15.12 9.84C15.12 9.66 15.165 9.489 15.243 9.339Z" fill="white"/>
      </svg>
      ,
      label: t("sidebar.performance"),
    },
    {
      key: 'advancedKey',
      icon: <svg width="27" height="27" viewBox="0 0 24 24" style={{marginRight: "17px"}} fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16.2856 9.86049C13.0633 9.86049 10.3955 12.231 9.9294 15.3248H6.85707C6.4419 15.3248 6.10707 14.99 6.10707 14.5748V4.82478H13.9017C14.2821 5.76763 15.2062 6.43192 16.2856 6.43192C17.7053 6.43192 18.8571 5.28013 18.8571 3.86049C18.8571 2.44085 17.7053 1.28906 16.2856 1.28906C15.2062 1.28906 14.2821 1.95335 13.9017 2.89621H1.28564V4.82478H4.1785V14.5748C4.1785 16.0533 5.3785 17.2533 6.85707 17.2533H9.9294C10.3955 20.3471 13.0633 22.7176 16.2856 22.7176C19.8348 22.7176 22.7142 19.8382 22.7142 16.2891C22.7142 12.74 19.8348 9.86049 16.2856 9.86049ZM19.7142 16.9855C19.7142 17.1033 19.6178 17.1998 19.4999 17.1998H17.1964V19.5033C17.1964 19.6212 17.0999 19.7176 16.9821 19.7176H15.5892C15.4714 19.7176 15.3749 19.6212 15.3749 19.5033V17.1998H13.0714C12.9535 17.1998 12.8571 17.1033 12.8571 16.9855V15.5926C12.8571 15.4748 12.9535 15.3783 13.0714 15.3783H15.3749V13.0748C15.3749 12.9569 15.4714 12.8605 15.5892 12.8605H16.9821C17.0999 12.8605 17.1964 12.9569 17.1964 13.0748V15.3783H19.4999C19.6178 15.3783 19.7142 15.4748 19.7142 15.5926V16.9855Z" fill="white"/>
      </svg>
      ,
      label: t("sidebar.advanced_key"),
    },
    {
      key: 'careerPreset',
      icon: <svg t="1736151069666" viewBox="0 0 1024 1024" style={{marginRight: '15px', marginLeft: '-6px'}} version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1667" width="36" height="36" ><path d="M512.001535 159.955038c38.595017 0 70.244854 31.622208 70.244854 70.298066 0 38.625716-31.649837 70.247924-70.244854 70.247924-38.648229 0-70.247924-31.622208-70.247924-70.247924C441.753611 191.577247 473.353306 159.955038 512.001535 159.955038M828.188824 405.898497l-210.793914 0 0 456.734302-70.244854 0 0-210.793914-70.298066 0 0 210.793914-70.252017 0 0-456.734302-210.790844 0 0-70.247924 632.378671 0L828.187801 405.898497z" p-id="1668" fill="#ffffff"></path></svg>
      ,
      label: t("sidebar.career_preset"),
    },
    {
      type: 'divider',
    },
    {
      key: 'keytest',
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="24"  height="24" style={{marginRight: "17px"}} viewBox="0 0 24 24" fill="none">
      <path d="M22.071 19.5008H3.64244V2.78655C3.64244 2.66869 3.54601 2.57227 3.42815 2.57227H1.92815C1.8103 2.57227 1.71387 2.66869 1.71387 2.78655V21.2151C1.71387 21.333 1.8103 21.4294 1.92815 21.4294H22.071C22.1889 21.4294 22.2853 21.333 22.2853 21.2151V19.7151C22.2853 19.5973 22.1889 19.5008 22.071 19.5008ZM5.99958 17.358H7.49958C7.61744 17.358 7.71387 17.2616 7.71387 17.1437V13.2866C7.71387 13.1687 7.61744 13.0723 7.49958 13.0723H5.99958C5.88172 13.0723 5.7853 13.1687 5.7853 13.2866V17.1437C5.7853 17.2616 5.88172 17.358 5.99958 17.358ZM10.071 17.358H11.571C11.6889 17.358 11.7853 17.2616 11.7853 17.1437V8.57227C11.7853 8.45441 11.6889 8.35798 11.571 8.35798H10.071C9.95315 8.35798 9.85673 8.45441 9.85673 8.57227V17.1437C9.85673 17.2616 9.95315 17.358 10.071 17.358ZM14.1424 17.358H15.6424C15.7603 17.358 15.8567 17.2616 15.8567 17.1437V10.6616C15.8567 10.5437 15.7603 10.4473 15.6424 10.4473H14.1424C14.0246 10.4473 13.9282 10.5437 13.9282 10.6616V17.1437C13.9282 17.2616 14.0246 17.358 14.1424 17.358ZM18.2139 17.358H19.7139C19.8317 17.358 19.9282 17.2616 19.9282 17.1437V6.42941C19.9282 6.31155 19.8317 6.21512 19.7139 6.21512H18.2139C18.096 6.21512 17.9996 6.31155 17.9996 6.42941V17.1437C17.9996 17.2616 18.096 17.358 18.2139 17.358Z" fill="#D9D9D9"/>
      </svg>,
      label: t('sidebar.keytest'),
    },
    {
      key: 'macro',
      icon: <svg width="24" height="24" viewBox="0 0 24 24" style={{marginRight: "20px"}} fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="white"/>
        <circle cx="12" cy="12" r="3" fill="white"/>
        <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="white" strokeWidth="1"/>
      </svg>,
      label: '宏键录制',
    }
    // {
    //   key: 'settings',
    //   icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    //   <path d="M22.9212 15.0506L21.1666 13.5505C21.2497 13.0415 21.2925 12.5219 21.2925 12.0022C21.2925 11.4825 21.2497 10.9628 21.1666 10.4539L22.9212 8.95375C23.0535 8.84045 23.1483 8.68956 23.1928 8.52112C23.2373 8.35269 23.2294 8.1747 23.1703 8.01082L23.1462 7.94118C22.6632 6.59115 21.9399 5.33966 21.0112 4.24717L20.963 4.19092C20.8504 4.05846 20.7002 3.96324 20.5324 3.91781C20.3645 3.87238 20.1869 3.87887 20.0228 3.93643L17.845 4.71059C17.0413 4.05162 16.1439 3.53194 15.1742 3.16763L14.7537 0.890687C14.7219 0.719359 14.6388 0.561742 14.5154 0.438775C14.3919 0.315807 14.234 0.233311 14.0626 0.202246L13.9902 0.188852C12.5946 -0.0629508 11.1266 -0.0629508 9.731 0.188852L9.65868 0.202246C9.48723 0.233311 9.32929 0.315807 9.20584 0.438775C9.0824 0.561742 8.99929 0.719359 8.96756 0.890687L8.54431 3.17835C7.58234 3.54273 6.68651 4.06214 5.89234 4.71595L3.69844 3.93643C3.53441 3.87842 3.35661 3.87169 3.18866 3.91715C3.02071 3.9626 2.87058 4.05809 2.7582 4.19092L2.70998 4.24717C1.78245 5.34044 1.05927 6.59172 0.575011 7.94118L0.550902 8.01082C0.430358 8.34567 0.529472 8.72069 0.800026 8.95375L2.57604 10.4699C2.493 10.9735 2.45282 11.4879 2.45282 11.9995C2.45282 12.5138 2.493 13.0281 2.57604 13.5291L0.800026 15.0452C0.667684 15.1585 0.572965 15.3094 0.528464 15.4779C0.483964 15.6463 0.49179 15.8243 0.550902 15.9882L0.575011 16.0578C1.05987 17.4079 1.77777 18.6535 2.70998 19.7518L2.7582 19.8081C2.87085 19.9405 3.02099 20.0357 3.18884 20.0812C3.35669 20.1266 3.53436 20.1201 3.69844 20.0626L5.89234 19.283C6.69061 19.9393 7.58264 20.459 8.54431 20.8206L8.96756 23.1083C8.99929 23.2796 9.0824 23.4372 9.20584 23.5602C9.32929 23.6832 9.48723 23.7657 9.65868 23.7967L9.731 23.8101C11.1395 24.0633 12.5818 24.0633 13.9902 23.8101L14.0626 23.7967C14.234 23.7657 14.3919 23.6832 14.5154 23.5602C14.6388 23.4372 14.7219 23.2796 14.7537 23.1083L15.1742 20.8314C16.1436 20.468 17.046 19.9466 17.845 19.2884L20.0228 20.0626C20.1868 20.1206 20.3646 20.1273 20.5326 20.0818C20.7005 20.0364 20.8507 19.9409 20.963 19.8081L21.0112 19.7518C21.9435 18.6508 22.6614 17.4079 23.1462 16.0578L23.1703 15.9882C23.2909 15.6587 23.1918 15.2837 22.9212 15.0506ZM19.2647 10.7699C19.3317 11.1744 19.3665 11.5896 19.3665 12.0049C19.3665 12.4201 19.3317 12.8353 19.2647 13.2398L19.0879 14.3139L21.0889 16.0257C20.7856 16.7245 20.4027 17.3861 19.9478 17.9972L17.4619 17.1159L16.6208 17.807C15.9805 18.3321 15.268 18.7446 14.4965 19.0339L13.4759 19.417L12.9964 22.0154C12.2398 22.1011 11.476 22.1011 10.7195 22.0154L10.24 19.4116L9.2274 19.0232C8.46395 18.7339 7.75408 18.3214 7.11922 17.799L6.27809 17.1052L3.77613 17.9946C3.32074 17.3811 2.94035 16.7195 2.63497 16.023L4.65744 14.2952L4.48332 13.2237C4.41903 12.8246 4.3842 12.412 4.3842 12.0049C4.3842 11.595 4.41635 11.1852 4.48332 10.786L4.65744 9.71451L2.63497 7.98671C2.93767 7.28756 3.32074 6.62858 3.77613 6.01515L6.27809 6.9045L7.11922 6.2107C7.75408 5.68834 8.46395 5.27581 9.2274 4.98651L10.2426 4.60344L10.7221 1.99969C11.4749 1.91397 12.2437 1.91397 12.9991 1.99969L13.4786 4.59809L14.4992 4.98115C15.268 5.27045 15.9832 5.68298 16.6234 6.20802L17.4646 6.89914L19.9505 6.01783C20.4059 6.63126 20.7862 7.29292 21.0916 7.98939L19.0906 9.70112L19.2647 10.7699ZM11.8633 7.02236C9.25954 7.02236 7.14868 9.13322 7.14868 11.737C7.14868 14.3407 9.25954 16.4516 11.8633 16.4516C14.467 16.4516 16.5779 14.3407 16.5779 11.737C16.5779 9.13322 14.467 7.02236 11.8633 7.02236ZM13.9849 13.8586C13.7066 14.1376 13.3759 14.3589 13.0118 14.5097C12.6477 14.6605 12.2574 14.7378 11.8633 14.7372C11.0623 14.7372 10.3096 14.4238 9.74172 13.8586C9.46265 13.5803 9.24136 13.2496 9.09057 12.8855C8.93978 12.5214 8.86247 12.1311 8.86309 11.737C8.86309 10.936 9.1765 10.1833 9.74172 9.6154C10.3096 9.0475 11.0623 8.73677 11.8633 8.73677C12.6642 8.73677 13.417 9.0475 13.9849 9.6154C14.2639 9.89366 14.4852 10.2244 14.636 10.5885C14.7868 10.9526 14.8641 11.3429 14.8635 11.737C14.8635 12.5379 14.5501 13.2907 13.9849 13.8586Z" fill="white"/>
    //   </svg>,
    //   label: t("sidebar.settings"),
    // }
  ];

  const handleMenuClick = (e) => {
    setMenuItem(e.key);
    if (e.key === 'keymap') {
      setCurrentLayer('00');
      setMaxSelection(1);
      setShowAdvancedKey(false);
      setShowLayerSwitch(true);
      setShowSelectButton(false);
      setShowKeyboard(true);
    } else if (e.key === 'light') {
      setCurrentLayer('00');
      setMaxSelection(87);
      setShowAdvancedKey(false);
      setShowLayerSwitch(false);
      setShowSelectButton(true);
      setShowKeyboard(true);
    } else if (e.key === 'performance') {
      setCurrentLayer('performance');
      setMaxSelection(87);
      setShowAdvancedKey(false);
      setShowLayerSwitch(false);
      setShowSelectButton(true);
      setShowKeyboard(true);
    } else if (e.key === 'advancedKey') {
      resetAdvancedKey();
      setCurrentLayer('00');
      setMaxSelection(0);
      setShowAdvancedKey(true);
      setShowLayerSwitch(false);
      setShowSelectButton(false);
      setShowKeyboard(true);
    } else if (e.key === 'careerPreset') {
      setCurrentLayer('performance');
      setMaxSelection(0);
      setShowAdvancedKey(false);
      setShowLayerSwitch(false);
      setShowSelectButton(false);
      setShowKeyboard(true);
    }else if (e.key === 'keytest') {
      setCurrentLayer('00');
      setMaxSelection(0);
      setShowAdvancedKey(false);
      setShowLayerSwitch(false);
      setShowSelectButton(false);
      setShowKeyboard(false);
    } else if (e.key === 'macro') {
      setCurrentLayer('00');
      setMaxSelection(0);
      setShowAdvancedKey(false);
      setShowLayerSwitch(false);
      setShowSelectButton(false);
      setShowKeyboard(false);
    } else {
      setCurrentLayer('00');
      setMaxSelection(1);
      setShowAdvancedKey(false);
      setShowLayerSwitch(false);
      setShowSelectButton(false);
      setShowKeyboard(false);
    }
    resetOtherStatus();
  }

  // 关键点：初始化高级键的地方
  const resetAdvancedKey = () => {
    setAdvancedKey({
      advancedKeyType: 'rs',
      rs: {
        list: []
      },
      socd: {
        list: []
      },
      mt: {
        list: []
      },
      dks: {
        list: []
      },
      tgl: {
        list: []
      },
      selectedIndex: null
    });

    Object.keys(data.keycaps['00']).forEach(key => {
      const [row, col] = key.split('-');
      updateKeycap(row, col, { advancedKeyType: '' }, '00');
    });

    Object.keys(data.keycaps['01']).forEach(key => {
      const [row, col] = key.split('-');
      updateKeycap(row, col, { advancedKeyType: '' }, '01');
    });

    // 获取RS
    addToQueue("2E")
    // 获取SOCD
    addToQueue("1E")
    // 获取MT
    addToQueue("22")
    // 获取DKS
    addToQueue("2A")
    // 获取TGL
    addToQueue("26")
  }

  const resetOtherStatus = () => {
    setCurrentSelectedKeycaps([]);

    // 重置性能配置选项
    setPerformance(prev => {
      return {
        ...prev,
        activeKey: ''
      }
    })
  }

  return (
    <>
      <Menu theme="dark"  className='main_menu'  mode="inline" defaultSelectedKeys={[data.menuItem]} onClick={handleMenuClick} items={items} />
      <div style={{position: 'fixed', bottom: '3.5em', marginLeft: '1em'}}>
        <Badge dot={newVersion}>
          <Button
            size="large"
            icon={<SettingOutlined />}
            type='default'
            key={'settings'}
            style={{backgroundColor:'#151619', borderColor: 'transparent', color: '#7A7B7C', width: '240px', fontSize: '14px'}}
            autoInsertSpace={false}
            onClick={() => {
              handleMenuClick({key: 'settings'});
            }}
          >
            {t('sidebar.settings')}
          </Button>
        </Badge>
      </div>
      <div style={{position: 'fixed', bottom: '1.4em', marginLeft: '3.7em', color: '#61636B'}}>
        IQUNIX FIRMWARE {firmwareVersion}
      </div>
    </>
  );
};

export default Sidebar;