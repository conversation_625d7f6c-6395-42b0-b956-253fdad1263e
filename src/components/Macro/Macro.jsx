import React, { useState, useEffect, useRef } from 'react';
import { Button, List, Card, Modal, message, Typography, Space, Tag, Divider } from 'antd';
import { PlayCircleOutlined, StopOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { setMacroConfig, resetMacroConfig, encodeMacroData, parseMacroData, createSampleMacroData } from './SetMacro';
import { getHIDKeyCode, getKeyName } from './MacroKeyMap';
import './Macro.css';

const { Title, Text } = Typography;

const Macro = () => {
  const { addToQueue, macro, setMacro } = useHandleDevice();
  const [isRecording, setIsRecording] = useState(false);
  const [currentMacro, setCurrentMacro] = useState(null);
  const [recordedActions, setRecordedActions] = useState([]);
  const [macroList, setMacroList] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedMacroIndex, setSelectedMacroIndex] = useState(null);
  const recordStartTime = useRef(null);
  const lastActionTime = useRef(null);
  const [messageApi, contextHolder] = message.useMessage();

  // 初始化M0-M15宏键列表
  useEffect(() => {
    const initialMacros = [];
    for (let i = 0; i < 16; i++) {
      initialMacros.push({
        id: `M${i}`,
        name: `M${i}`,
        actions: [],
        description: `宏键 ${i}`
      });
    }
    setMacroList(initialMacros);
  }, []);

  // 监听键盘事件进行录制
  useEffect(() => {
    if (!isRecording) return;

    const handleKeyDown = (event) => {
      if (!isRecording) return;

      const now = Date.now();
      if (!recordStartTime.current) {
        recordStartTime.current = now;
        lastActionTime.current = now;
      }

      // 计算与上一个动作的时间间隔
      const timeSinceLastAction = now - lastActionTime.current;

      // 如果间隔大于10ms，添加延时动作
      if (timeSinceLastAction > 10 && recordedActions.length > 0) {
        setRecordedActions(prev => [...prev, {
          type: 'delay',
          duration: timeSinceLastAction,
          timestamp: now - recordStartTime.current
        }]);
      }

      // 添加按键按下动作
      const hidKeyCode = getHIDKeyCode(event.keyCode);
      if (hidKeyCode !== 0x00) {
        setRecordedActions(prev => [...prev, {
          type: 'keydown',
          keyCode: hidKeyCode,
          jsKeyCode: event.keyCode,
          key: getKeyName(event.keyCode),
          timestamp: now - recordStartTime.current
        }]);
      }

      lastActionTime.current = now;
      event.preventDefault();
    };

    const handleKeyUp = (event) => {
      if (!isRecording) return;

      const now = Date.now();
      const timeSinceLastAction = now - lastActionTime.current;

      // 如果间隔大于10ms，添加延时动作
      if (timeSinceLastAction > 10) {
        setRecordedActions(prev => [...prev, {
          type: 'delay',
          duration: timeSinceLastAction,
          timestamp: now - recordStartTime.current
        }]);
      }

      // 添加按键抬起动作
      const hidKeyCode = getHIDKeyCode(event.keyCode);
      if (hidKeyCode !== 0x00) {
        setRecordedActions(prev => [...prev, {
          type: 'keyup',
          keyCode: hidKeyCode,
          jsKeyCode: event.keyCode,
          key: getKeyName(event.keyCode),
          timestamp: now - recordStartTime.current
        }]);
      }

      lastActionTime.current = now;
      event.preventDefault();
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isRecording, recordedActions]);

  const startRecording = (macroIndex) => {
    setCurrentMacro(macroIndex);
    setRecordedActions([]);
    setIsRecording(true);
    recordStartTime.current = null;
    lastActionTime.current = null;
    messageApi.info(`开始录制 M${macroIndex}，按任意键开始录制...`);
  };

  const stopRecording = () => {
    setIsRecording(false);
    if (recordedActions.length > 0) {
      // 保存录制的动作到对应的宏键
      setMacroList(prev => {
        const newList = [...prev];
        newList[currentMacro] = {
          ...newList[currentMacro],
          actions: recordedActions,
          description: `包含 ${recordedActions.filter(a => a.type === 'keydown').length} 个按键动作`
        };
        return newList;
      });

      // 发送到设备
      saveMacroToDevice(currentMacro, recordedActions);
      messageApi.success(`M${currentMacro} 录制完成！`);
    } else {
      messageApi.warning('没有录制到任何动作');
    }
    setCurrentMacro(null);
    setRecordedActions([]);
  };

  const saveMacroToDevice = (macroIndex, actions) => {
    try {
      const encodedData = encodeMacroData(actions);
      const offset = macroIndex * 64; // 每个宏键64字节空间

      // 分批发送数据
      for (let i = 0; i < encodedData.length; i += 28) {
        const chunk = encodedData.slice(i, i + 28);
        setMacroConfig(addToQueue, offset + i, chunk);
      }
    } catch (error) {
      messageApi.error('保存宏键失败：' + error.message);
    }
  };

  const deleteMacro = (macroIndex) => {
    Modal.confirm({
      title: `确认删除 M${macroIndex}？`,
      content: '删除后将无法恢复',
      onOk: () => {
        setMacroList(prev => {
          const newList = [...prev];
          newList[macroIndex] = {
            ...newList[macroIndex],
            actions: [],
            description: `宏键 ${macroIndex}`
          };
          return newList;
        });

        // 清除设备中的宏键数据
        const offset = macroIndex * 64;
        setMacroConfig(addToQueue, offset, [0x00]); // 发送结束标识清除宏键

        messageApi.success(`M${macroIndex} 已删除`);
      }
    });
  };

  const playMacro = (macroIndex) => {
    const macro = macroList[macroIndex];
    if (macro.actions.length === 0) {
      messageApi.warning('该宏键没有录制内容');
      return;
    }

    messageApi.info(`播放 M${macroIndex}`);
    // 这里可以添加播放宏键的逻辑
  };

  const testSampleMacro = () => {
    // 发送示例宏键数据到M0
    const sampleData = createSampleMacroData();

    // 首先重置宏键配置
    resetMacroConfig(addToQueue);

    // 设置M0的宏键数据
    setMacroConfig(addToQueue, 0, sampleData);

    messageApi.success('已发送示例宏键数据到M0（按两次A键，间隔50ms）');
  };

  const formatActionType = (action) => {
    switch (action.type) {
      case 'keydown':
        return <Tag color="green">按下 {action.key}</Tag>;
      case 'keyup':
        return <Tag color="red">抬起 {action.key}</Tag>;
      case 'delay':
        return <Tag color="blue">延时 {action.duration}ms</Tag>;
      default:
        return <Tag>{action.type}</Tag>;
    }
  };

  return (
    <div className="macro-container">
      {contextHolder}
      <div style={{width: '100%', maxWidth: '1120px', marginTop: '2em'}}>
        <div className="d-flex align-items-center" style={{justifyContent: 'space-between'}}>
          <div className="d-flex align-items-center">
            <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em'}}></div>
            <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>宏键录制</div>
          </div>
          <Button type="primary" onClick={testSampleMacro} disabled={isRecording}>
            测试示例宏键
          </Button>
        </div>
      </div>

      <div className="macro-content">
        <div className="macro-list">
          <Title level={4}>宏键列表 (M0-M15)</Title>
          <List
            grid={{ gutter: 16, column: 4 }}
            dataSource={macroList}
            renderItem={(item, index) => (
              <List.Item>
                <Card
                  title={item.name}
                  size="small"
                  actions={[
                    <Button
                      key="record"
                      type={isRecording && currentMacro === index ? "primary" : "default"}
                      icon={isRecording && currentMacro === index ? <StopOutlined /> : <PlayCircleOutlined />}
                      onClick={() => {
                        if (isRecording && currentMacro === index) {
                          stopRecording();
                        } else if (!isRecording) {
                          startRecording(index);
                        }
                      }}
                      disabled={isRecording && currentMacro !== index}
                    >
                      {isRecording && currentMacro === index ? '停止' : '录制'}
                    </Button>,
                    <Button
                      key="play"
                      icon={<PlayCircleOutlined />}
                      onClick={() => playMacro(index)}
                      disabled={item.actions.length === 0 || isRecording}
                    >
                      播放
                    </Button>,
                    <Button
                      key="delete"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => deleteMacro(index)}
                      disabled={item.actions.length === 0 || isRecording}
                    >
                      删除
                    </Button>
                  ]}
                >
                  <Text type="secondary">{item.description}</Text>
                  {item.actions.length > 0 && (
                    <div style={{marginTop: '8px'}}>
                      <Text strong>动作数: {item.actions.length}</Text>
                    </div>
                  )}
                </Card>
              </List.Item>
            )}
          />
        </div>

        {isRecording && (
          <div className="recording-panel">
            <Card title={`正在录制 M${currentMacro}`} style={{marginTop: '20px'}}>
              <Space direction="vertical" style={{width: '100%'}}>
                <Text>已录制 {recordedActions.length} 个动作</Text>
                <div style={{maxHeight: '200px', overflow: 'auto'}}>
                  {recordedActions.map((action, index) => (
                    <div key={index} style={{marginBottom: '4px'}}>
                      {formatActionType(action)}
                      <Text type="secondary" style={{marginLeft: '8px'}}>
                        {action.timestamp}ms
                      </Text>
                    </div>
                  ))}
                </div>
                <Button type="primary" danger onClick={stopRecording}>
                  停止录制
                </Button>
              </Space>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default Macro;
