const SetMacro = (hexArray, setMacro) => {
  if (hexArray[0] === '0C') {
    // 获取键盘总的最大可设置macro数目响应
    const maxMacroCount = parseInt(hexArray[1], 16);
    setMacro(prev => ({ ...prev, maxMacroCount }));
  } else if (hexArray[0] === '0D') {
    // 获取键盘macro缓存大小响应
    const bufferSize = (parseInt(hexArray[1], 16) << 8) | parseInt(hexArray[2], 16);
    setMacro(prev => ({ ...prev, bufferSize }));
  } else if (hexArray[0] === '0E') {
    // 获取键盘macro配置响应
    const offsetHigh = parseInt(hexArray[1], 16);
    const offsetLow = parseInt(hexArray[2], 16);
    const dataLength = parseInt(hexArray[3], 16);
    const offset = (offsetHigh << 8) | offsetLow;

    // 提取数据部分（暂时不使用，但保留用于将来扩展）
    // const data = hexArray.slice(4, 4 + dataLength);

    // 更新macro数据缓存
    setMacro(prev => {
      const newBuffer = { ...prev.buffer };
      for (let i = 0; i < dataLength; i++) {
        newBuffer[offset + i] = hexArray[4 + i];
      }
      return { ...prev, buffer: newBuffer };
    });
  }
};

// 设置宏键配置数据
export const setMacroConfig = (dataQueue, offset, data) => {
  const offsetHigh = (offset >> 8) & 0xFF;
  const offsetLow = offset & 0xFF;
  const dataLength = data.length;

  const offsetHighHex = offsetHigh.toString(16).padStart(2, '0').toUpperCase();
  const offsetLowHex = offsetLow.toString(16).padStart(2, '0').toUpperCase();
  const dataLengthHex = dataLength.toString(16).padStart(2, '0').toUpperCase();

  // 构建命令：0x0F + 偏移高8位 + 偏移低8位 + 数据长度 + 数据
  let command = `0F ${offsetHighHex} ${offsetLowHex} ${dataLengthHex}`;

  // 添加数据
  data.forEach(byte => {
    command += ` ${byte.toString(16).padStart(2, '0').toUpperCase()}`;
  });

  // 补足32字节
  const currentLength = 4 + dataLength;
  for (let i = currentLength; i < 32; i++) {
    command += ' 00';
  }

  dataQueue.addToQueue(command);
};

// 重置宏键配置
export const resetMacroConfig = (dataQueue) => {
  dataQueue.addToQueue("10 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
};

// 解析宏键数据
export const parseMacroData = (buffer, macroIndex) => {
  // 每个宏键的起始位置计算
  const startOffset = macroIndex * 64; // 假设每个宏键最多64字节

  const macroData = [];
  let offset = startOffset;

  while (offset < startOffset + 64 && buffer[offset]) {
    const actionType = parseInt(buffer[offset], 16);

    if (actionType === 0x01) {
      // 按键按下
      const keyCode = parseInt(buffer[offset + 1], 16);
      macroData.push({
        type: 'keydown',
        keyCode: keyCode,
        timestamp: 0
      });
      offset += 2;
    } else if (actionType === 0x02) {
      // 按键抬起
      const keyCode = parseInt(buffer[offset + 1], 16);
      macroData.push({
        type: 'keyup',
        keyCode: keyCode,
        timestamp: 0
      });
      offset += 2;
    } else if (actionType === 0x03) {
      // 延时
      const delayMs = (parseInt(buffer[offset + 1], 16) << 8) | parseInt(buffer[offset + 2], 16);
      macroData.push({
        type: 'delay',
        duration: delayMs,
        timestamp: 0
      });
      offset += 3;
    } else {
      break;
    }
  }

  return macroData;
};

// 编码宏键数据
export const encodeMacroData = (macroActions) => {
  const data = [];

  macroActions.forEach(action => {
    if (action.type === 'keydown') {
      data.push(0x01); // 按键按下标识
      data.push(0x02); // 修饰符（暂时固定为0x02）
      data.push(0x04); // 固定值
      data.push(0x01); // 固定值
      data.push(0x04); // 固定值
      data.push(action.keyCode); // HID键码
      data.push(0x30); // 固定值（可能是时间相关）
      data.push(0x7C); // 固定值
    } else if (action.type === 'keyup') {
      data.push(0x01); // 按键抬起标识
      data.push(0x03); // 修饰符（抬起为0x03）
      data.push(0x04); // 固定值
      data.push(0x01); // 固定值
      data.push(0x04); // 固定值
      data.push(action.keyCode); // HID键码
      data.push(0x30); // 固定值（可能是时间相关）
      data.push(0x7C); // 固定值
    } else if (action.type === 'delay') {
      // 延时处理 - 根据示例，50ms的延时
      // const delayMs = Math.min(action.duration, 255); // 限制在255ms内
      data.push(0x35); // 延时标识（根据示例）
      data.push(0x30); // 固定值
      data.push(0x7C); // 固定值
    }
  });

  // 结束标识
  data.push(0x00);

  return data;
};

// 创建示例宏键数据（按两次A键，中间间隔50ms）
export const createSampleMacroData = () => {
  return [
    0x01, 0x02, 0x04, 0x01, 0x04, 0x04, 0x30, 0x7C, // A键按下
    0x01, 0x03, 0x04, 0x01, 0x04, 0x04, 0x30, 0x7C, // A键抬起
    0x01, 0x02, 0x04, 0x01, 0x04, 0x04, 0x30, 0x7C, // A键按下
    0x01, 0x03, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00  // A键抬起并结束
  ];
};

export default SetMacro;
